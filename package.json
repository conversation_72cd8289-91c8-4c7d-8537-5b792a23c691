{"name": "npm-analyze", "version": "1.0.0", "description": "NPM 包数据排序工具 - 按包名称和版本号对 Excel 文件中的 npm 包数据进行排序", "main": "index.js", "bin": {"npm-analyze": "./index.js"}, "scripts": {"start": "node index.js", "test": "node index.js npm-test.xlsx", "help": "node index.js --help"}, "keywords": ["npm", "excel", "sort", "semver", "packages"], "author": "", "license": "ISC", "dependencies": {"semver": "^7.7.2", "xlsx": "^0.18.5"}}