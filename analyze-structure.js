const XLSX = require('xlsx');
const path = require('path');

function analyzeExcelStructure(filePath) {
    try {
        console.log('正在分析文件:', filePath);
        
        // 读取 Excel 文件
        const workbook = XLSX.readFile(filePath);
        
        // 获取工作表名称
        const sheetNames = workbook.SheetNames;
        console.log('工作表数量:', sheetNames.length);
        console.log('工作表名称:', sheetNames);
        
        // 分析第一个工作表
        const firstSheetName = sheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];
        
        console.log('\n=== 分析第一个工作表:', firstSheetName, '===');
        
        // 获取工作表范围
        const range = XLSX.utils.decode_range(worksheet['!ref']);
        console.log('数据范围:', worksheet['!ref']);
        console.log('行数:', range.e.r + 1);
        console.log('列数:', range.e.c + 1);
        
        // 转换为 JSON 格式查看数据结构
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        
        console.log('\n=== 前5行数据预览 ===');
        for (let i = 0; i < Math.min(5, jsonData.length); i++) {
            console.log(`第${i + 1}行:`, jsonData[i]);
        }
        
        // 分析列结构
        console.log('\n=== 列结构分析 ===');
        if (jsonData.length > 0) {
            const headerRow = jsonData[0];
            headerRow.forEach((header, index) => {
                console.log(`第${index + 1}列: "${header}"`);
            });
        }
        
        // 检查数据类型
        console.log('\n=== 数据类型分析 ===');
        if (jsonData.length > 1) {
            const dataRow = jsonData[1];
            dataRow.forEach((value, index) => {
                console.log(`第${index + 1}列数据类型: ${typeof value}, 示例值: "${value}"`);
            });
        }
        
        return {
            sheetNames,
            range,
            rowCount: range.e.r + 1,
            colCount: range.e.c + 1,
            data: jsonData
        };
        
    } catch (error) {
        console.error('分析文件时出错:', error.message);
        return null;
    }
}

// 分析 npm-test.xlsx 文件
const filePath = path.join(__dirname, 'npm-test.xlsx');
analyzeExcelStructure(filePath);
