const { cleanVersion, compareVersions } = require('./npm-analyzer');
const semver = require('semver');

/**
 * 测试版本号清理功能
 */
function testVersionCleaning() {
    console.log('=== 测试版本号清理功能 ===');
    
    const testCases = [
        '7.0.0 - rc.1',
        '7.26.0',
        '7.14.6',
        '7.9.0',
        '7.8.4',
        '7.7.7',
        '7.4.5',
        '7.0.0',
        '7.0.0-2',
        'v1.2.3',
        '^1.2.3',
        '~1.2.3',
        '1.0.0-alpha.1',
        '1.0.0-beta.2',
        '1.0.0-rc.3',
        '2.0.0-1',
        '2.0.0-2'
    ];
    
    testCases.forEach(version => {
        const cleaned = cleanVersion(version);
        const isValid = semver.valid(cleaned);
        console.log(`${version.padEnd(15)} -> ${cleaned.padEnd(15)} (${isValid ? '✅' : '❌'})`);
    });
}

/**
 * 测试版本号排序逻辑
 */
function testVersionSorting() {
    console.log('\n=== 测试版本号排序逻辑 ===');
    
    // 测试同一个包的不同版本
    const babelVersions = [
        '7.0.0 - rc.1',
        '7.26.0',
        '7.14.6',
        '7.9.0',
        '7.8.4',
        '7.7.7',
        '7.4.5',
        '7.0.0',
        '7.0.0-2',
        '7.0.0-alpha.1',
        '7.0.0-beta.1',
        '7.0.0-rc.2'
    ];
    
    console.log('原始顺序:');
    babelVersions.forEach((version, index) => {
        console.log(`  ${index + 1}. ${version}`);
    });
    
    // 排序
    const sortedVersions = [...babelVersions].sort((a, b) => compareVersions(a, b));
    
    console.log('\n排序后顺序（降序，最新版本在前）:');
    sortedVersions.forEach((version, index) => {
        const cleaned = cleanVersion(version);
        console.log(`  ${index + 1}. ${version.padEnd(15)} (${cleaned})`);
    });
    
    // 验证排序规则
    console.log('\n=== 验证排序规则 ===');
    
    // 测试正式版 vs 预发布版
    const testPairs = [
        ['7.0.0', '7.0.0-rc.1'],
        ['7.0.0-rc.1', '7.0.0-beta.1'],
        ['7.0.0-beta.1', '7.0.0-alpha.1'],
        ['7.0.0-rc.2', '7.0.0-rc.1'],
        ['7.1.0', '7.0.0'],
        ['7.0.0-2', '7.0.0'],
        ['2.0.0-2', '2.0.0-1']
    ];
    
    testPairs.forEach(([versionA, versionB]) => {
        const result = compareVersions(versionA, versionB);
        const winner = result < 0 ? versionA : versionB;
        console.log(`${versionA} vs ${versionB} -> ${winner} 优先 (${result < 0 ? 'A' : 'B'})`);
    });
}

/**
 * 测试完整的包数据排序
 */
function testPackageSorting() {
    console.log('\n=== 测试完整的包数据排序 ===');
    
    const testData = [
        { name: '@babel/core', version: '7.0.0 - rc.1', other: 'test1' },
        { name: '@babel/core', version: '7.26.0', other: 'test2' },
        { name: '@babel/core', version: '7.14.6', other: 'test3' },
        { name: '@alicloud/arms', version: '1.0.2', other: 'test4' },
        { name: '@babel/core', version: '7.0.0', other: 'test5' },
        { name: '@babel/core', version: '7.0.0-2', other: 'test6' },
        { name: '@babel/core', version: '7.0.0-alpha.1', other: 'test7' },
        { name: '@babel/core', version: '7.0.0-beta.1', other: 'test8' },
        { name: '@babel/core', version: '7.0.0-rc.2', other: 'test9' }
    ];
    
    console.log('排序前:');
    testData.forEach((item, index) => {
        console.log(`  ${index + 1}. ${item.name} | ${item.version} | ${item.other}`);
    });
    
    // 使用我们的排序逻辑
    const sortedData = [...testData].sort((a, b) => {
        // 主排序：包名称按字母升序
        const nameA = (a.name || '').toLowerCase();
        const nameB = (b.name || '').toLowerCase();
        
        if (nameA < nameB) return -1;
        if (nameA > nameB) return 1;
        
        // 次排序：相同包名下，版本号按自定义规则降序
        return compareVersions(a.version, b.version);
    });
    
    console.log('\n排序后:');
    sortedData.forEach((item, index) => {
        console.log(`  ${index + 1}. ${item.name} | ${item.version} | ${item.other}`);
    });
}

// 运行所有测试
console.log('🧪 开始版本号排序测试\n');
testVersionCleaning();
testVersionSorting();
testPackageSorting();
console.log('\n✅ 测试完成');
