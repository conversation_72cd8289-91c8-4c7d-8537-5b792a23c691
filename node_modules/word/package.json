{"_from": "word@~0.3.0", "_id": "word@0.3.0", "_inBundle": false, "_integrity": "sha512-OELeY0Q61OXpdUfTp+oweA/vtLVg5VDOXh+3he3PNzLGG/y0oylSOC1xRVj0+l4vQ3tj/bB1HVHv1ocXkQceFA==", "_location": "/word", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "word@~0.3.0", "name": "word", "escapedName": "word", "rawSpec": "~0.3.0", "saveSpec": null, "fetchSpec": "~0.3.0"}, "_requiredBy": ["/xlsx"], "_resolved": "https://registry.npmmirror.com/word/-/word-0.3.0.tgz", "_shasum": "8542157e4f8e849f4a363a288992d47612db9961", "_spec": "word@~0.3.0", "_where": "/Users/<USER>/code/mucfc/work/tool/npm-analyze/node_modules/xlsx", "author": {"name": "sheetjs"}, "bugs": {"url": "https://github.com/SheetJS/js-word/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Word Processing Document library", "devDependencies": {}, "engines": {"node": ">=0.8"}, "homepage": "https://wordjs.com/", "keywords": ["word"], "license": "Apache-2.0", "main": "./word", "name": "word", "repository": {"type": "git", "url": "git://github.com/SheetJS/js-word.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "version": "0.3.0"}