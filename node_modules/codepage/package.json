{"_from": "codepage@~1.15.0", "_id": "codepage@1.15.0", "_inBundle": false, "_integrity": "sha512-3g6NUTPd/YtuuGrhMnOMRjFc+LJw/bnMp3+0r/Wcz3IXUuCosKRJvMphm5+Q+bvTVGcJJuRvVLuYba+WojaFaA==", "_location": "/codepage", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "codepage@~1.15.0", "name": "codepage", "escapedName": "codepage", "rawSpec": "~1.15.0", "saveSpec": null, "fetchSpec": "~1.15.0"}, "_requiredBy": ["/xlsx"], "_resolved": "https://registry.npmmirror.com/codepage/-/codepage-1.15.0.tgz", "_shasum": "2e00519024b39424ec66eeb3ec07227e692618ab", "_spec": "codepage@~1.15.0", "_where": "/Users/<USER>/code/mucfc/work/tool/npm-analyze/node_modules/xlsx", "alex": {"allow": ["chinese", "european", "german", "japanese", "latin"]}, "author": {"name": "SheetJS"}, "browser": {"buffer": "false"}, "bugs": {"url": "https://github.com/SheetJS/js-codepage/issues"}, "bundleDependencies": false, "config": {"blanket": {"pattern": "[cputils.js]"}}, "deprecated": false, "description": "pure-JS library to handle codepages", "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/commander": "^2.12.0", "@types/node": "^8.0.7", "blanket": "~1.2.3", "dtslint": "^0.1.2", "mocha": "~2.5.3", "typescript": "2.2.0", "voc": "~1.1.0"}, "engines": {"node": ">=0.8"}, "files": ["LICENSE", "README.md", "bin", "bits/*.js", "types/index.d.ts", "types/*.json", "cptable.js", "cputils.js", "dist/sbcs.full.js", "dist/cpexcel.full.js"], "homepage": "https://sheetjs.com/", "keywords": ["codepage", "iconv", "convert", "strings"], "license": "Apache-2.0", "main": "cputils.js", "name": "codepage", "repository": {"type": "git", "url": "git://github.com/SheetJS/js-codepage.git"}, "scripts": {"build": "make js", "dtslint": "dtslint types", "lint": "make fullint", "pretest": "git submodule init && git submodule update", "test": "make test"}, "types": "types", "version": "1.15.0"}