#!/usr/bin/env node

const {
    readExcelFile,
    sortData,
    showSortingStats,
    writeExcelFile,
    validateOutputPath,
    validateData
} = require('./npm-analyzer');
const path = require('path');

/**
 * 显示使用说明
 */
function showUsage() {
    console.log(`
npm-analyze - NPM 包数据排序工具

用法:
  node index.js <输入文件> <输出文件>
  node index.js <输入文件>  (输出文件将自动命名为 sorted_<输入文件名>)

参数:
  输入文件    要处理的 Excel 文件路径 (.xlsx)
  输出文件    排序后的输出文件路径 (.xlsx)

示例:
  node index.js npm-test.xlsx sorted-npm-test.xlsx
  node index.js npm-test.xlsx
  node index.js ./data/packages.xlsx ./output/sorted-packages.xlsx

功能:
  - 按包名称字母升序排列
  - 相同包名下按版本号 semver 降序排列（最新版本在前）
  - 保持每行数据的完整性
    `);
}

/**
 * 生成默认输出文件名
 * @param {string} inputPath - 输入文件路径
 * @returns {string} 默认输出文件路径
 */
function generateDefaultOutputPath(inputPath) {
    const parsedPath = path.parse(inputPath);
    return path.join(parsedPath.dir, `sorted_${parsedPath.name}${parsedPath.ext}`);
}

/**
 * 主处理函数
 * @param {string} inputPath - 输入文件路径
 * @param {string} outputPath - 输出文件路径
 */
async function processFile(inputPath, outputPath) {
    const startTime = Date.now();
    
    try {
        console.log('=== NPM 包数据排序工具 ===\n');
        
        // 1. 读取 Excel 文件
        const { headers, data } = readExcelFile(inputPath);
        
        // 2. 验证数据格式
        validateData(headers, data);
        
        // 3. 排序数据
        const sortedData = sortData(data);
        
        // 4. 显示排序统计
        showSortingStats(sortedData);
        
        // 5. 验证输出路径
        const validatedOutputPath = validateOutputPath(outputPath);
        
        // 6. 写入排序后的数据
        writeExcelFile(headers, sortedData, validatedOutputPath);
        
        const endTime = Date.now();
        const duration = ((endTime - startTime) / 1000).toFixed(2);
        
        console.log(`\n=== 处理完成 ===`);
        console.log(`输入文件: ${inputPath}`);
        console.log(`输出文件: ${validatedOutputPath}`);
        console.log(`处理时间: ${duration} 秒`);
        console.log(`总行数: ${sortedData.length}`);
        
    } catch (error) {
        console.error('\n❌ 处理失败:', error.message);
        process.exit(1);
    }
}

/**
 * 主函数
 */
function main() {
    const args = process.argv.slice(2);
    
    // 检查参数
    if (args.length === 0 || args.includes('--help') || args.includes('-h')) {
        showUsage();
        return;
    }
    
    if (args.length < 1 || args.length > 2) {
        console.error('❌ 参数数量错误');
        showUsage();
        process.exit(1);
    }
    
    const inputPath = args[0];
    let outputPath = args[1];
    
    // 如果没有指定输出文件，生成默认文件名
    if (!outputPath) {
        outputPath = generateDefaultOutputPath(inputPath);
        console.log(`未指定输出文件，将使用默认文件名: ${outputPath}`);
    }
    
    // 开始处理
    processFile(inputPath, outputPath);
}

// 运行主函数
if (require.main === module) {
    main();
}

module.exports = {
    processFile,
    generateDefaultOutputPath,
    showUsage
};
